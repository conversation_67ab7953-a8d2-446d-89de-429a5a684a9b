import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON>, Star } from "lucide-react";

const plans = [
  {
    name: "Starter",
    description: "Perfect for small teams getting started",
    price: "$0",
    period: "forever",
    features: [
      "Up to 5 team members",
      "3 projects",
      "Basic task management",
      "Team chat",
      "Calendar integration",
      "Mobile apps",
      "Email support"
    ],
    cta: "Get Started Free",
    href: "/auth/signup?plan=starter",
    popular: false
  },
  {
    name: "Professional",
    description: "For growing teams that need more power",
    price: "$12",
    period: "per user/month",
    features: [
      "Unlimited team members",
      "Unlimited projects",
      "Advanced task management",
      "Time tracking",
      "Document management",
      "Analytics & reporting",
      "Automation workflows",
      "Goal tracking",
      "Priority support"
    ],
    cta: "Start Free Trial",
    href: "/auth/signup?plan=professional",
    popular: true
  },
  {
    name: "Enterprise",
    description: "For large organizations with advanced needs",
    price: "$24",
    period: "per user/month",
    features: [
      "Everything in Professional",
      "Advanced security (SSO, 2FA)",
      "Custom integrations",
      "Dedicated account manager",
      "Advanced analytics",
      "Custom workflows",
      "API access",
      "24/7 phone support",
      "SLA guarantee"
    ],
    cta: "Contact Sales",
    href: "/contact?plan=enterprise",
    popular: false
  }
];

export function PricingSection() {
  return (
    <section id="pricing" className="py-20 sm:py-32 bg-muted/20">
      <div className="container">
        <div className="mx-auto max-w-2xl text-center">
          <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">
            Simple, transparent pricing
          </h2>
          <p className="mt-6 text-lg leading-8 text-muted-foreground">
            Choose the plan that&apos;s right for your team. All plans include a 14-day free trial.
          </p>
        </div>
        
        <div className="mx-auto mt-16 grid max-w-5xl grid-cols-1 gap-8 lg:grid-cols-3">
          {plans.map((plan, index) => (
            <Card key={index} className={`relative ${plan.popular ? 'border-primary shadow-lg scale-105' : ''}`}>
              {plan.popular && (
                <div className="absolute -top-4 left-1/2 -translate-x-1/2">
                  <Badge className="flex items-center gap-1 px-3 py-1">
                    <Star className="h-3 w-3 fill-current" />
                    Most Popular
                  </Badge>
                </div>
              )}
              
              <CardHeader className="text-center">
                <CardTitle className="text-2xl">{plan.name}</CardTitle>
                <CardDescription className="text-base">{plan.description}</CardDescription>
                <div className="mt-4">
                  <span className="text-4xl font-bold">{plan.price}</span>
                  <span className="text-muted-foreground">/{plan.period}</span>
                </div>
              </CardHeader>
              
              <CardContent>
                <ul className="space-y-3">
                  {plan.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-center gap-3">
                      <Check className="h-4 w-4 text-green-500 flex-shrink-0" />
                      <span className="text-sm">{feature}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
              
              <CardFooter>
                <Button 
                  className="w-full" 
                  variant={plan.popular ? "default" : "outline"}
                  size="lg"
                  asChild
                >
                  <Link href={plan.href}>{plan.cta}</Link>
                </Button>
              </CardFooter>
            </Card>
          ))}
        </div>
        
        <div className="mt-16 text-center">
          <p className="text-sm text-muted-foreground">
            All plans include SSL encryption, daily backups, and 99.9% uptime guarantee.
          </p>
          <p className="mt-2 text-sm text-muted-foreground">
            Need a custom plan? <Link href="/contact" className="text-primary hover:underline">Contact our sales team</Link>
          </p>
        </div>
      </div>
    </section>
  );
}

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { DashboardNav } from "@/components/dashboard-nav";
import { 
  FolderKanban, 
  Users, 
  Clock, 
  CheckCircle, 
  TrendingUp, 
  Calendar,
  Plus,
  ArrowRight,
  AlertCircle
} from "lucide-react";

const stats = [
  {
    title: "Active Projects",
    value: "12",
    change: "+2 from last month",
    icon: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    trend: "up"
  },
  {
    title: "Team Members",
    value: "8",
    change: "+1 from last month",
    icon: Users,
    trend: "up"
  },
  {
    title: "Hours Tracked",
    value: "156",
    change: "This week",
    icon: Clock,
    trend: "neutral"
  },
  {
    title: "Tasks Completed",
    value: "89",
    change: "+12% from last week",
    icon: CheckCircle,
    trend: "up"
  }
];

const recentProjects = [
  {
    name: "Website Redesign",
    status: "In Progress",
    progress: 75,
    dueDate: "Dec 15, 2024",
    team: [
      { name: "<PERSON>", avatar: "/avatars/01.png" },
      { name: "Bob <PERSON>", avatar: "/avatars/02.png" },
      { name: "Carol <PERSON>", avatar: "/avatars/03.png" }
    ]
  },
  {
    name: "Mobile App Development",
    status: "Planning",
    progress: 25,
    dueDate: "Jan 30, 2025",
    team: [
      { name: "David Wilson", avatar: "/avatars/04.png" },
      { name: "Eva Brown", avatar: "/avatars/05.png" }
    ]
  },
  {
    name: "Marketing Campaign",
    status: "Review",
    progress: 90,
    dueDate: "Dec 8, 2024",
    team: [
      { name: "Frank Miller", avatar: "/avatars/06.png" },
      { name: "Grace Lee", avatar: "/avatars/07.png" },
      { name: "Henry Taylor", avatar: "/avatars/08.png" }
    ]
  }
];

const upcomingTasks = [
  {
    title: "Review design mockups",
    project: "Website Redesign",
    dueDate: "Today",
    priority: "High"
  },
  {
    title: "Client presentation",
    project: "Marketing Campaign",
    dueDate: "Tomorrow",
    priority: "High"
  },
  {
    title: "Code review",
    project: "Mobile App Development",
    dueDate: "Dec 10",
    priority: "Medium"
  },
  {
    title: "Update documentation",
    project: "Website Redesign",
    dueDate: "Dec 12",
    priority: "Low"
  }
];

export default function DashboardPage() {
  return (
    <div className="min-h-screen bg-muted/20">
      <DashboardNav />
      
      <main className="container py-8">
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold">Welcome back, John!</h1>
            <p className="text-muted-foreground">
              Here&apos;s what&apos;s happening with your projects today.
            </p>
          </div>
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            New Project
          </Button>
        </div>

        {/* Stats Grid */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4 mb-8">
          {stats.map((stat, index) => (
            <Card key={index}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  {stat.title}
                </CardTitle>
                <stat.icon className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stat.value}</div>
                <p className="text-xs text-muted-foreground flex items-center">
                  {stat.trend === "up" && <TrendingUp className="h-3 w-3 mr-1 text-green-500" />}
                  {stat.change}
                </p>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="grid gap-6 lg:grid-cols-3">
          {/* Recent Projects */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle>Recent Projects</CardTitle>
                <CardDescription>
                  Your most active projects and their current status
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {recentProjects.map((project, index) => (
                  <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2">
                        <h3 className="font-medium">{project.name}</h3>
                        <Badge variant={
                          project.status === "In Progress" ? "default" :
                          project.status === "Planning" ? "secondary" :
                          "outline"
                        }>
                          {project.status}
                        </Badge>
                      </div>
                      <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                        <span className="flex items-center">
                          <Calendar className="h-3 w-3 mr-1" />
                          Due {project.dueDate}
                        </span>
                        <span>{project.progress}% complete</span>
                      </div>
                      <div className="flex -space-x-2">
                        {project.team.map((member, memberIndex) => (
                          <Avatar key={memberIndex} className="h-6 w-6 border-2 border-background">
                            <AvatarImage src={member.avatar} alt={member.name} />
                            <AvatarFallback className="text-xs">
                              {member.name.split(' ').map(n => n[0]).join('')}
                            </AvatarFallback>
                          </Avatar>
                        ))}
                      </div>
                    </div>
                    <Button variant="ghost" size="sm">
                      <ArrowRight className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </CardContent>
            </Card>
          </div>

          {/* Upcoming Tasks */}
          <div>
            <Card>
              <CardHeader>
                <CardTitle>Upcoming Tasks</CardTitle>
                <CardDescription>
                  Tasks that need your attention
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {upcomingTasks.map((task, index) => (
                  <div key={index} className="flex items-start space-x-3 p-3 border rounded-lg">
                    <div className="flex-1 space-y-1">
                      <h4 className="text-sm font-medium">{task.title}</h4>
                      <p className="text-xs text-muted-foreground">{task.project}</p>
                      <div className="flex items-center space-x-2">
                        <Badge 
                          variant={
                            task.priority === "High" ? "destructive" :
                            task.priority === "Medium" ? "default" :
                            "secondary"
                          }
                          className="text-xs"
                        >
                          {task.priority}
                        </Badge>
                        <span className="text-xs text-muted-foreground">
                          {task.dueDate}
                        </span>
                      </div>
                    </div>
                    {task.priority === "High" && (
                      <AlertCircle className="h-4 w-4 text-red-500 mt-1" />
                    )}
                  </div>
                ))}
                <Button variant="outline" className="w-full">
                  View All Tasks
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </main>
    </div>
  );
}

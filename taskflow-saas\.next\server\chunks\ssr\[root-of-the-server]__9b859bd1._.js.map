{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/vs%20code/shadcn/taskflow-saas/src/components/navigation.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Navigation = registerClientReference(\n    function() { throw new Error(\"Attempted to call Navigation() from the server but Navigation is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/navigation.tsx <module evaluation>\",\n    \"Navigation\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,aAAa,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,+DACA", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/vs%20code/shadcn/taskflow-saas/src/components/navigation.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Navigation = registerClientReference(\n    function() { throw new Error(\"Attempted to call Navigation() from the server but Navigation is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/navigation.tsx\",\n    \"Navigation\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,aAAa,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,2CACA", "debugId": null}}, {"offset": {"line": 42, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 50, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/vs%20code/shadcn/taskflow-saas/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 64, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/vs%20code/shadcn/taskflow-saas/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 119, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/vs%20code/shadcn/taskflow-saas/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 163, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/vs%20code/shadcn/taskflow-saas/src/components/hero-section.tsx"], "sourcesContent": ["import Link from \"next/link\";\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { ArrowRight, Play, CheckCircle, Users, Clock, BarChart3 } from \"lucide-react\";\n\nexport function HeroSection() {\n  return (\n    <section className=\"relative overflow-hidden bg-gradient-to-b from-background to-muted/20 py-20 sm:py-32\">\n      <div className=\"container relative\">\n        <div className=\"mx-auto max-w-4xl text-center\">\n          <Badge variant=\"secondary\" className=\"mb-6\">\n            🚀 New: AI-powered task automation now available\n          </Badge>\n          \n          <h1 className=\"text-4xl font-bold tracking-tight sm:text-6xl lg:text-7xl\">\n            Streamline Your\n            <span className=\"text-primary\"> Team&apos;s Workflow</span>\n          </h1>\n          \n          <p className=\"mt-6 text-lg leading-8 text-muted-foreground sm:text-xl\">\n            TaskFlow helps teams collaborate more effectively with powerful project management tools, \n            real-time collaboration, and intelligent automation. Get more done with less effort.\n          </p>\n          \n          <div className=\"mt-10 flex items-center justify-center gap-x-6\">\n            <Button size=\"lg\" asChild>\n              <Link href=\"/auth/signup\">\n                Start Free Trial\n                <ArrowRight className=\"ml-2 h-4 w-4\" />\n              </Link>\n            </Button>\n            <Button variant=\"outline\" size=\"lg\">\n              <Play className=\"mr-2 h-4 w-4\" />\n              Watch Demo\n            </Button>\n          </div>\n          \n          <div className=\"mt-8 flex items-center justify-center gap-x-8 text-sm text-muted-foreground\">\n            <div className=\"flex items-center gap-x-2\">\n              <CheckCircle className=\"h-4 w-4 text-green-500\" />\n              <span>14-day free trial</span>\n            </div>\n            <div className=\"flex items-center gap-x-2\">\n              <CheckCircle className=\"h-4 w-4 text-green-500\" />\n              <span>No credit card required</span>\n            </div>\n            <div className=\"flex items-center gap-x-2\">\n              <CheckCircle className=\"h-4 w-4 text-green-500\" />\n              <span>Cancel anytime</span>\n            </div>\n          </div>\n        </div>\n        \n        {/* Stats Section */}\n        <div className=\"mt-20 grid grid-cols-1 gap-8 sm:grid-cols-3\">\n          <div className=\"text-center\">\n            <div className=\"flex items-center justify-center w-12 h-12 mx-auto mb-4 bg-primary/10 rounded-lg\">\n              <Users className=\"h-6 w-6 text-primary\" />\n            </div>\n            <div className=\"text-3xl font-bold\">50K+</div>\n            <div className=\"text-sm text-muted-foreground\">Active Teams</div>\n          </div>\n          <div className=\"text-center\">\n            <div className=\"flex items-center justify-center w-12 h-12 mx-auto mb-4 bg-primary/10 rounded-lg\">\n              <Clock className=\"h-6 w-6 text-primary\" />\n            </div>\n            <div className=\"text-3xl font-bold\">2M+</div>\n            <div className=\"text-sm text-muted-foreground\">Hours Saved</div>\n          </div>\n          <div className=\"text-center\">\n            <div className=\"flex items-center justify-center w-12 h-12 mx-auto mb-4 bg-primary/10 rounded-lg\">\n              <BarChart3 className=\"h-6 w-6 text-primary\" />\n            </div>\n            <div className=\"text-3xl font-bold\">99.9%</div>\n            <div className=\"text-sm text-muted-foreground\">Uptime</div>\n          </div>\n        </div>\n      </div>\n      \n      {/* Background decoration */}\n      <div className=\"absolute inset-x-0 -top-40 -z-10 transform-gpu overflow-hidden blur-3xl sm:-top-80\">\n        <div className=\"relative left-[calc(50%-11rem)] aspect-[1155/678] w-[36.125rem] -translate-x-1/2 rotate-[30deg] bg-gradient-to-tr from-primary to-secondary opacity-20 sm:left-[calc(50%-30rem)] sm:w-[72.1875rem]\" />\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;AAEO,SAAS;IACd,qBACE,8OAAC;QAAQ,WAAU;;0BACjB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;gCAAY,WAAU;0CAAO;;;;;;0CAI5C,8OAAC;gCAAG,WAAU;;oCAA4D;kDAExE,8OAAC;wCAAK,WAAU;kDAAe;;;;;;;;;;;;0CAGjC,8OAAC;gCAAE,WAAU;0CAA0D;;;;;;0CAKvE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCAAC,MAAK;wCAAK,OAAO;kDACvB,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;;gDAAe;8DAExB,8OAAC,kNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAG1B,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,MAAK;;0DAC7B,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;0CAKrC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,2NAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;0DACvB,8OAAC;0DAAK;;;;;;;;;;;;kDAER,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,2NAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;0DACvB,8OAAC;0DAAK;;;;;;;;;;;;kDAER,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,2NAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;0DACvB,8OAAC;0DAAK;;;;;;;;;;;;;;;;;;;;;;;;kCAMZ,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;kDAEnB,8OAAC;wCAAI,WAAU;kDAAqB;;;;;;kDACpC,8OAAC;wCAAI,WAAU;kDAAgC;;;;;;;;;;;;0CAEjD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;kDAEnB,8OAAC;wCAAI,WAAU;kDAAqB;;;;;;kDACpC,8OAAC;wCAAI,WAAU;kDAAgC;;;;;;;;;;;;0CAEjD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,kNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;;;;;;kDAEvB,8OAAC;wCAAI,WAAU;kDAAqB;;;;;;kDACpC,8OAAC;wCAAI,WAAU;kDAAgC;;;;;;;;;;;;;;;;;;;;;;;;0BAMrD,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;;;;;;;;;;;;;;;;AAIvB", "debugId": null}}, {"offset": {"line": 519, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/vs%20code/shadcn/taskflow-saas/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 614, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/vs%20code/shadcn/taskflow-saas/src/components/features-section.tsx"], "sourcesContent": ["import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardTitle } from \"@/components/ui/card\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { \n  Kanban, \n  Users, \n  Clock, \n  MessageSquare, \n  FileText, \n  BarChart3, \n  Shield, \n  Zap,\n  Calendar,\n  Target,\n  Globe,\n  Smartphone\n} from \"lucide-react\";\n\nconst features = [\n  {\n    icon: Kanban,\n    title: \"Project Management\",\n    description: \"Organize tasks with Kanban boards, Gantt charts, and custom workflows. Track progress and deadlines effortlessly.\",\n    badge: \"Core\"\n  },\n  {\n    icon: Users,\n    title: \"Team Collaboration\",\n    description: \"Real-time collaboration with team members. Share files, assign tasks, and communicate seamlessly.\",\n    badge: \"Core\"\n  },\n  {\n    icon: Clock,\n    title: \"Time Tracking\",\n    description: \"Built-in time tracking for accurate project billing and productivity insights. Generate detailed reports.\",\n    badge: \"Pro\"\n  },\n  {\n    icon: MessageSquare,\n    title: \"Team Chat\",\n    description: \"Integrated messaging system with channels, direct messages, and file sharing capabilities.\",\n    badge: \"Core\"\n  },\n  {\n    icon: FileText,\n    title: \"Document Management\",\n    description: \"Centralized document storage with version control, comments, and collaborative editing.\",\n    badge: \"Pro\"\n  },\n  {\n    icon: BarChart3,\n    title: \"Analytics & Reporting\",\n    description: \"Comprehensive analytics dashboard with custom reports and performance metrics.\",\n    badge: \"Pro\"\n  },\n  {\n    icon: Shield,\n    title: \"Enterprise Security\",\n    description: \"Bank-level security with SSO, 2FA, and compliance with SOC 2 and GDPR standards.\",\n    badge: \"Enterprise\"\n  },\n  {\n    icon: Zap,\n    title: \"Automation\",\n    description: \"Automate repetitive tasks with custom workflows, triggers, and integrations.\",\n    badge: \"Pro\"\n  },\n  {\n    icon: Calendar,\n    title: \"Calendar Integration\",\n    description: \"Sync with Google Calendar, Outlook, and other calendar apps for seamless scheduling.\",\n    badge: \"Core\"\n  },\n  {\n    icon: Target,\n    title: \"Goal Tracking\",\n    description: \"Set and track team goals with OKRs, milestones, and progress visualization.\",\n    badge: \"Pro\"\n  },\n  {\n    icon: Globe,\n    title: \"Global Access\",\n    description: \"Access your workspace from anywhere with cloud sync and offline capabilities.\",\n    badge: \"Core\"\n  },\n  {\n    icon: Smartphone,\n    title: \"Mobile Apps\",\n    description: \"Native iOS and Android apps with full feature parity and push notifications.\",\n    badge: \"Core\"\n  }\n];\n\nexport function FeaturesSection() {\n  return (\n    <section id=\"features\" className=\"py-20 sm:py-32\">\n      <div className=\"container\">\n        <div className=\"mx-auto max-w-2xl text-center\">\n          <h2 className=\"text-3xl font-bold tracking-tight sm:text-4xl\">\n            Everything you need to manage your team\n          </h2>\n          <p className=\"mt-6 text-lg leading-8 text-muted-foreground\">\n            TaskFlow provides all the tools your team needs to collaborate effectively, \n            track progress, and deliver projects on time.\n          </p>\n        </div>\n        \n        <div className=\"mx-auto mt-16 grid max-w-7xl grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3\">\n          {features.map((feature, index) => (\n            <Card key={index} className=\"relative overflow-hidden\">\n              <CardHeader>\n                <div className=\"flex items-center justify-between\">\n                  <div className=\"flex items-center justify-center w-12 h-12 bg-primary/10 rounded-lg\">\n                    <feature.icon className=\"h-6 w-6 text-primary\" />\n                  </div>\n                  <Badge \n                    variant={feature.badge === \"Enterprise\" ? \"default\" : feature.badge === \"Pro\" ? \"secondary\" : \"outline\"}\n                    className=\"text-xs\"\n                  >\n                    {feature.badge}\n                  </Badge>\n                </div>\n                <CardTitle className=\"text-xl\">{feature.title}</CardTitle>\n              </CardHeader>\n              <CardContent>\n                <CardDescription className=\"text-base\">\n                  {feature.description}\n                </CardDescription>\n              </CardContent>\n            </Card>\n          ))}\n        </div>\n        \n        <div className=\"mt-16 text-center\">\n          <p className=\"text-sm text-muted-foreground\">\n            And many more features to help your team succeed\n          </p>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;AAeA,MAAM,WAAW;IACf;QACE,MAAM,sMAAA,CAAA,SAAM;QACZ,OAAO;QACP,aAAa;QACb,OAAO;IACT;IACA;QACE,MAAM,oMAAA,CAAA,QAAK;QACX,OAAO;QACP,aAAa;QACb,OAAO;IACT;IACA;QACE,MAAM,oMAAA,CAAA,QAAK;QACX,OAAO;QACP,aAAa;QACb,OAAO;IACT;IACA;QACE,MAAM,wNAAA,CAAA,gBAAa;QACnB,OAAO;QACP,aAAa;QACb,OAAO;IACT;IACA;QACE,MAAM,8MAAA,CAAA,WAAQ;QACd,OAAO;QACP,aAAa;QACb,OAAO;IACT;IACA;QACE,MAAM,kNAAA,CAAA,YAAS;QACf,OAAO;QACP,aAAa;QACb,OAAO;IACT;IACA;QACE,MAAM,sMAAA,CAAA,SAAM;QACZ,OAAO;QACP,aAAa;QACb,OAAO;IACT;IACA;QACE,MAAM,gMAAA,CAAA,MAAG;QACT,OAAO;QACP,aAAa;QACb,OAAO;IACT;IACA;QACE,MAAM,0MAAA,CAAA,WAAQ;QACd,OAAO;QACP,aAAa;QACb,OAAO;IACT;IACA;QACE,MAAM,sMAAA,CAAA,SAAM;QACZ,OAAO;QACP,aAAa;QACb,OAAO;IACT;IACA;QACE,MAAM,oMAAA,CAAA,QAAK;QACX,OAAO;QACP,aAAa;QACb,OAAO;IACT;IACA;QACE,MAAM,8MAAA,CAAA,aAAU;QAChB,OAAO;QACP,aAAa;QACb,OAAO;IACT;CACD;AAEM,SAAS;IACd,qBACE,8OAAC;QAAQ,IAAG;QAAW,WAAU;kBAC/B,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAgD;;;;;;sCAG9D,8OAAC;4BAAE,WAAU;sCAA+C;;;;;;;;;;;;8BAM9D,8OAAC;oBAAI,WAAU;8BACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC,gIAAA,CAAA,OAAI;4BAAa,WAAU;;8CAC1B,8OAAC,gIAAA,CAAA,aAAU;;sDACT,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,QAAQ,IAAI;wDAAC,WAAU;;;;;;;;;;;8DAE1B,8OAAC,iIAAA,CAAA,QAAK;oDACJ,SAAS,QAAQ,KAAK,KAAK,eAAe,YAAY,QAAQ,KAAK,KAAK,QAAQ,cAAc;oDAC9F,WAAU;8DAET,QAAQ,KAAK;;;;;;;;;;;;sDAGlB,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAW,QAAQ,KAAK;;;;;;;;;;;;8CAE/C,8OAAC,gIAAA,CAAA,cAAW;8CACV,cAAA,8OAAC,gIAAA,CAAA,kBAAe;wCAAC,WAAU;kDACxB,QAAQ,WAAW;;;;;;;;;;;;2BAjBf;;;;;;;;;;8BAwBf,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;kCAAgC;;;;;;;;;;;;;;;;;;;;;;AAOvD", "debugId": null}}, {"offset": {"line": 851, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/vs%20code/shadcn/taskflow-saas/src/components/pricing-section.tsx"], "sourcesContent": ["import Link from \"next/link\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { <PERSON>, Star } from \"lucide-react\";\n\nconst plans = [\n  {\n    name: \"Starter\",\n    description: \"Perfect for small teams getting started\",\n    price: \"$0\",\n    period: \"forever\",\n    features: [\n      \"Up to 5 team members\",\n      \"3 projects\",\n      \"Basic task management\",\n      \"Team chat\",\n      \"Calendar integration\",\n      \"Mobile apps\",\n      \"Email support\"\n    ],\n    cta: \"Get Started Free\",\n    href: \"/auth/signup?plan=starter\",\n    popular: false\n  },\n  {\n    name: \"Professional\",\n    description: \"For growing teams that need more power\",\n    price: \"$12\",\n    period: \"per user/month\",\n    features: [\n      \"Unlimited team members\",\n      \"Unlimited projects\",\n      \"Advanced task management\",\n      \"Time tracking\",\n      \"Document management\",\n      \"Analytics & reporting\",\n      \"Automation workflows\",\n      \"Goal tracking\",\n      \"Priority support\"\n    ],\n    cta: \"Start Free Trial\",\n    href: \"/auth/signup?plan=professional\",\n    popular: true\n  },\n  {\n    name: \"Enterprise\",\n    description: \"For large organizations with advanced needs\",\n    price: \"$24\",\n    period: \"per user/month\",\n    features: [\n      \"Everything in Professional\",\n      \"Advanced security (SSO, 2FA)\",\n      \"Custom integrations\",\n      \"Dedicated account manager\",\n      \"Advanced analytics\",\n      \"Custom workflows\",\n      \"API access\",\n      \"24/7 phone support\",\n      \"SLA guarantee\"\n    ],\n    cta: \"Contact Sales\",\n    href: \"/contact?plan=enterprise\",\n    popular: false\n  }\n];\n\nexport function PricingSection() {\n  return (\n    <section id=\"pricing\" className=\"py-20 sm:py-32 bg-muted/20\">\n      <div className=\"container\">\n        <div className=\"mx-auto max-w-2xl text-center\">\n          <h2 className=\"text-3xl font-bold tracking-tight sm:text-4xl\">\n            Simple, transparent pricing\n          </h2>\n          <p className=\"mt-6 text-lg leading-8 text-muted-foreground\">\n            Choose the plan that&apos;s right for your team. All plans include a 14-day free trial.\n          </p>\n        </div>\n        \n        <div className=\"mx-auto mt-16 grid max-w-5xl grid-cols-1 gap-8 lg:grid-cols-3\">\n          {plans.map((plan, index) => (\n            <Card key={index} className={`relative ${plan.popular ? 'border-primary shadow-lg scale-105' : ''}`}>\n              {plan.popular && (\n                <div className=\"absolute -top-4 left-1/2 -translate-x-1/2\">\n                  <Badge className=\"flex items-center gap-1 px-3 py-1\">\n                    <Star className=\"h-3 w-3 fill-current\" />\n                    Most Popular\n                  </Badge>\n                </div>\n              )}\n              \n              <CardHeader className=\"text-center\">\n                <CardTitle className=\"text-2xl\">{plan.name}</CardTitle>\n                <CardDescription className=\"text-base\">{plan.description}</CardDescription>\n                <div className=\"mt-4\">\n                  <span className=\"text-4xl font-bold\">{plan.price}</span>\n                  <span className=\"text-muted-foreground\">/{plan.period}</span>\n                </div>\n              </CardHeader>\n              \n              <CardContent>\n                <ul className=\"space-y-3\">\n                  {plan.features.map((feature, featureIndex) => (\n                    <li key={featureIndex} className=\"flex items-center gap-3\">\n                      <Check className=\"h-4 w-4 text-green-500 flex-shrink-0\" />\n                      <span className=\"text-sm\">{feature}</span>\n                    </li>\n                  ))}\n                </ul>\n              </CardContent>\n              \n              <CardFooter>\n                <Button \n                  className=\"w-full\" \n                  variant={plan.popular ? \"default\" : \"outline\"}\n                  size=\"lg\"\n                  asChild\n                >\n                  <Link href={plan.href}>{plan.cta}</Link>\n                </Button>\n              </CardFooter>\n            </Card>\n          ))}\n        </div>\n        \n        <div className=\"mt-16 text-center\">\n          <p className=\"text-sm text-muted-foreground\">\n            All plans include SSL encryption, daily backups, and 99.9% uptime guarantee.\n          </p>\n          <p className=\"mt-2 text-sm text-muted-foreground\">\n            Need a custom plan? <Link href=\"/contact\" className=\"text-primary hover:underline\">Contact our sales team</Link>\n          </p>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AAAA;;;;;;;AAEA,MAAM,QAAQ;IACZ;QACE,MAAM;QACN,aAAa;QACb,OAAO;QACP,QAAQ;QACR,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,KAAK;QACL,MAAM;QACN,SAAS;IACX;IACA;QACE,MAAM;QACN,aAAa;QACb,OAAO;QACP,QAAQ;QACR,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,KAAK;QACL,MAAM;QACN,SAAS;IACX;IACA;QACE,MAAM;QACN,aAAa;QACb,OAAO;QACP,QAAQ;QACR,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,KAAK;QACL,MAAM;QACN,SAAS;IACX;CACD;AAEM,SAAS;IACd,qBACE,8OAAC;QAAQ,IAAG;QAAU,WAAU;kBAC9B,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAgD;;;;;;sCAG9D,8OAAC;4BAAE,WAAU;sCAA+C;;;;;;;;;;;;8BAK9D,8OAAC;oBAAI,WAAU;8BACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC,gIAAA,CAAA,OAAI;4BAAa,WAAW,CAAC,SAAS,EAAE,KAAK,OAAO,GAAG,uCAAuC,IAAI;;gCAChG,KAAK,OAAO,kBACX,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;wCAAC,WAAU;;0DACf,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAyB;;;;;;;;;;;;8CAM/C,8OAAC,gIAAA,CAAA,aAAU;oCAAC,WAAU;;sDACpB,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAY,KAAK,IAAI;;;;;;sDAC1C,8OAAC,gIAAA,CAAA,kBAAe;4CAAC,WAAU;sDAAa,KAAK,WAAW;;;;;;sDACxD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAsB,KAAK,KAAK;;;;;;8DAChD,8OAAC;oDAAK,WAAU;;wDAAwB;wDAAE,KAAK,MAAM;;;;;;;;;;;;;;;;;;;8CAIzD,8OAAC,gIAAA,CAAA,cAAW;8CACV,cAAA,8OAAC;wCAAG,WAAU;kDACX,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,6BAC3B,8OAAC;gDAAsB,WAAU;;kEAC/B,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;kEACjB,8OAAC;wDAAK,WAAU;kEAAW;;;;;;;+CAFpB;;;;;;;;;;;;;;;8CAQf,8OAAC,gIAAA,CAAA,aAAU;8CACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCACL,WAAU;wCACV,SAAS,KAAK,OAAO,GAAG,YAAY;wCACpC,MAAK;wCACL,OAAO;kDAEP,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAM,KAAK,IAAI;sDAAG,KAAK,GAAG;;;;;;;;;;;;;;;;;2BArC3B;;;;;;;;;;8BA4Cf,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,WAAU;sCAAgC;;;;;;sCAG7C,8OAAC;4BAAE,WAAU;;gCAAqC;8CAC5B,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAW,WAAU;8CAA+B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM/F", "debugId": null}}, {"offset": {"line": 1165, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/vs%20code/shadcn/taskflow-saas/src/components/ui/separator.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Separator = registerClientReference(\n    function() { throw new Error(\"Attempted to call Separator() from the server but Separator is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/separator.tsx <module evaluation>\",\n    \"Separator\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,YAAY,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,iEACA", "debugId": null}}, {"offset": {"line": 1177, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/vs%20code/shadcn/taskflow-saas/src/components/ui/separator.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Separator = registerClientReference(\n    function() { throw new Error(\"Attempted to call Separator() from the server but Separator is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/separator.tsx\",\n    \"Separator\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,YAAY,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,6CACA", "debugId": null}}, {"offset": {"line": 1189, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1197, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/vs%20code/shadcn/taskflow-saas/src/components/footer.tsx"], "sourcesContent": ["import Link from \"next/link\";\nimport { Separator } from \"@/components/ui/separator\";\nimport { Zap, Twitter, Github, Linkedin, Mail } from \"lucide-react\";\n\nconst footerLinks = {\n  product: [\n    { name: \"Features\", href: \"#features\" },\n    { name: \"Pricing\", href: \"#pricing\" },\n    { name: \"Integrations\", href: \"/integrations\" },\n    { name: \"API\", href: \"/api\" },\n    { name: \"Changelog\", href: \"/changelog\" }\n  ],\n  company: [\n    { name: \"About\", href: \"/about\" },\n    { name: \"Blog\", href: \"/blog\" },\n    { name: \"Careers\", href: \"/careers\" },\n    { name: \"Contact\", href: \"/contact\" },\n    { name: \"Press\", href: \"/press\" }\n  ],\n  resources: [\n    { name: \"Documentation\", href: \"/docs\" },\n    { name: \"Help Center\", href: \"/help\" },\n    { name: \"Community\", href: \"/community\" },\n    { name: \"Templates\", href: \"/templates\" },\n    { name: \"Webinars\", href: \"/webinars\" }\n  ],\n  legal: [\n    { name: \"Privacy Policy\", href: \"/privacy\" },\n    { name: \"Terms of Service\", href: \"/terms\" },\n    { name: \"Cookie Policy\", href: \"/cookies\" },\n    { name: \"Security\", href: \"/security\" },\n    { name: \"GDPR\", href: \"/gdpr\" }\n  ]\n};\n\nconst socialLinks = [\n  { name: \"Twitter\", href: \"https://twitter.com/taskflow\", icon: Twitter },\n  { name: \"GitHub\", href: \"https://github.com/taskflow\", icon: Github },\n  { name: \"LinkedIn\", href: \"https://linkedin.com/company/taskflow\", icon: Linkedin },\n  { name: \"Email\", href: \"mailto:<EMAIL>\", icon: Mail }\n];\n\nexport function Footer() {\n  return (\n    <footer className=\"border-t bg-background\">\n      <div className=\"container py-16\">\n        <div className=\"grid grid-cols-1 gap-8 lg:grid-cols-6\">\n          {/* Brand */}\n          <div className=\"lg:col-span-2\">\n            <Link href=\"/\" className=\"flex items-center space-x-2\">\n              <Zap className=\"h-6 w-6 text-primary\" />\n              <span className=\"font-bold text-xl\">TaskFlow</span>\n            </Link>\n            <p className=\"mt-4 text-sm text-muted-foreground max-w-xs\">\n              Streamline your team&apos;s workflow with powerful project management tools \n              and real-time collaboration features.\n            </p>\n            <div className=\"mt-6 flex space-x-4\">\n              {socialLinks.map((social) => (\n                <Link\n                  key={social.name}\n                  href={social.href}\n                  className=\"text-muted-foreground hover:text-foreground transition-colors\"\n                  target=\"_blank\"\n                  rel=\"noopener noreferrer\"\n                >\n                  <social.icon className=\"h-5 w-5\" />\n                  <span className=\"sr-only\">{social.name}</span>\n                </Link>\n              ))}\n            </div>\n          </div>\n\n          {/* Links */}\n          <div>\n            <h3 className=\"font-semibold\">Product</h3>\n            <ul className=\"mt-4 space-y-3\">\n              {footerLinks.product.map((link) => (\n                <li key={link.name}>\n                  <Link\n                    href={link.href}\n                    className=\"text-sm text-muted-foreground hover:text-foreground transition-colors\"\n                  >\n                    {link.name}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          <div>\n            <h3 className=\"font-semibold\">Company</h3>\n            <ul className=\"mt-4 space-y-3\">\n              {footerLinks.company.map((link) => (\n                <li key={link.name}>\n                  <Link\n                    href={link.href}\n                    className=\"text-sm text-muted-foreground hover:text-foreground transition-colors\"\n                  >\n                    {link.name}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          <div>\n            <h3 className=\"font-semibold\">Resources</h3>\n            <ul className=\"mt-4 space-y-3\">\n              {footerLinks.resources.map((link) => (\n                <li key={link.name}>\n                  <Link\n                    href={link.href}\n                    className=\"text-sm text-muted-foreground hover:text-foreground transition-colors\"\n                  >\n                    {link.name}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          <div>\n            <h3 className=\"font-semibold\">Legal</h3>\n            <ul className=\"mt-4 space-y-3\">\n              {footerLinks.legal.map((link) => (\n                <li key={link.name}>\n                  <Link\n                    href={link.href}\n                    className=\"text-sm text-muted-foreground hover:text-foreground transition-colors\"\n                  >\n                    {link.name}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n        </div>\n\n        <Separator className=\"my-8\" />\n\n        <div className=\"flex flex-col items-center justify-between gap-4 md:flex-row\">\n          <p className=\"text-sm text-muted-foreground\">\n            © 2024 TaskFlow. All rights reserved.\n          </p>\n          <p className=\"text-sm text-muted-foreground\">\n            Made with ❤️ for productive teams everywhere\n          </p>\n        </div>\n      </div>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;;;;;AAEA,MAAM,cAAc;IAClB,SAAS;QACP;YAAE,MAAM;YAAY,MAAM;QAAY;QACtC;YAAE,MAAM;YAAW,MAAM;QAAW;QACpC;YAAE,MAAM;YAAgB,MAAM;QAAgB;QAC9C;YAAE,MAAM;YAAO,MAAM;QAAO;QAC5B;YAAE,MAAM;YAAa,MAAM;QAAa;KACzC;IACD,SAAS;QACP;YAAE,MAAM;YAAS,MAAM;QAAS;QAChC;YAAE,MAAM;YAAQ,MAAM;QAAQ;QAC9B;YAAE,MAAM;YAAW,MAAM;QAAW;QACpC;YAAE,MAAM;YAAW,MAAM;QAAW;QACpC;YAAE,MAAM;YAAS,MAAM;QAAS;KACjC;IACD,WAAW;QACT;YAAE,MAAM;YAAiB,MAAM;QAAQ;QACvC;YAAE,MAAM;YAAe,MAAM;QAAQ;QACrC;YAAE,MAAM;YAAa,MAAM;QAAa;QACxC;YAAE,MAAM;YAAa,MAAM;QAAa;QACxC;YAAE,MAAM;YAAY,MAAM;QAAY;KACvC;IACD,OAAO;QACL;YAAE,MAAM;YAAkB,MAAM;QAAW;QAC3C;YAAE,MAAM;YAAoB,MAAM;QAAS;QAC3C;YAAE,MAAM;YAAiB,MAAM;QAAW;QAC1C;YAAE,MAAM;YAAY,MAAM;QAAY;QACtC;YAAE,MAAM;YAAQ,MAAM;QAAQ;KAC/B;AACH;AAEA,MAAM,cAAc;IAClB;QAAE,MAAM;QAAW,MAAM;QAAgC,MAAM,wMAAA,CAAA,UAAO;IAAC;IACvE;QAAE,MAAM;QAAU,MAAM;QAA+B,MAAM,sMAAA,CAAA,SAAM;IAAC;IACpE;QAAE,MAAM;QAAY,MAAM;QAAyC,MAAM,0MAAA,CAAA,WAAQ;IAAC;IAClF;QAAE,MAAM;QAAS,MAAM;QAA6B,MAAM,kMAAA,CAAA,OAAI;IAAC;CAChE;AAEM,SAAS;IACd,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;;sDACvB,8OAAC,gMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;sDACf,8OAAC;4CAAK,WAAU;sDAAoB;;;;;;;;;;;;8CAEtC,8OAAC;oCAAE,WAAU;8CAA8C;;;;;;8CAI3D,8OAAC;oCAAI,WAAU;8CACZ,YAAY,GAAG,CAAC,CAAC,uBAChB,8OAAC,4JAAA,CAAA,UAAI;4CAEH,MAAM,OAAO,IAAI;4CACjB,WAAU;4CACV,QAAO;4CACP,KAAI;;8DAEJ,8OAAC,OAAO,IAAI;oDAAC,WAAU;;;;;;8DACvB,8OAAC;oDAAK,WAAU;8DAAW,OAAO,IAAI;;;;;;;2CAPjC,OAAO,IAAI;;;;;;;;;;;;;;;;sCAcxB,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAgB;;;;;;8CAC9B,8OAAC;oCAAG,WAAU;8CACX,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,qBACxB,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;;;;;;2CALL,KAAK,IAAI;;;;;;;;;;;;;;;;sCAYxB,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAgB;;;;;;8CAC9B,8OAAC;oCAAG,WAAU;8CACX,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,qBACxB,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;;;;;;2CALL,KAAK,IAAI;;;;;;;;;;;;;;;;sCAYxB,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAgB;;;;;;8CAC9B,8OAAC;oCAAG,WAAU;8CACX,YAAY,SAAS,CAAC,GAAG,CAAC,CAAC,qBAC1B,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;;;;;;2CALL,KAAK,IAAI;;;;;;;;;;;;;;;;sCAYxB,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAgB;;;;;;8CAC9B,8OAAC;oCAAG,WAAU;8CACX,YAAY,KAAK,CAAC,GAAG,CAAC,CAAC,qBACtB,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;;;;;;2CALL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;8BAa1B,8OAAC,qIAAA,CAAA,YAAS;oBAAC,WAAU;;;;;;8BAErB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,WAAU;sCAAgC;;;;;;sCAG7C,8OAAC;4BAAE,WAAU;sCAAgC;;;;;;;;;;;;;;;;;;;;;;;AAOvD", "debugId": null}}, {"offset": {"line": 1615, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/vs%20code/shadcn/taskflow-saas/src/app/page.tsx"], "sourcesContent": ["import { Navigation } from \"@/components/navigation\";\nimport { HeroSection } from \"@/components/hero-section\";\nimport { FeaturesSection } from \"@/components/features-section\";\nimport { PricingSection } from \"@/components/pricing-section\";\nimport { Footer } from \"@/components/footer\";\n\nexport default function Home() {\n  return (\n    <div className=\"min-h-screen\">\n      <Navigation />\n      <main>\n        <HeroSection />\n        <FeaturesSection />\n        <PricingSection />\n      </main>\n      <Footer />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;;;;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,gIAAA,CAAA,aAAU;;;;;0BACX,8OAAC;;kCACC,8OAAC,qIAAA,CAAA,cAAW;;;;;kCACZ,8OAAC,yIAAA,CAAA,kBAAe;;;;;kCAChB,8OAAC,wIAAA,CAAA,iBAAc;;;;;;;;;;;0BAEjB,8OAAC,4HAAA,CAAA,SAAM;;;;;;;;;;;AAGb", "debugId": null}}]}
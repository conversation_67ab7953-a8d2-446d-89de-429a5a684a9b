# TaskFlow - Modern SaaS Project Management Platform

A complete, modern, and fully functional SaaS website built with Next.js, TypeScript, and shadcn/ui components. TaskFlow is a project management and team collaboration platform designed to streamline workflows and boost productivity.

## 🚀 Features

### Core Functionality
- **Landing Page**: Professional hero section, features showcase, and pricing tiers
- **Authentication**: Complete auth flow with sign up, sign in, and password reset
- **Dashboard**: Comprehensive dashboard with project overview and analytics
- **User Profile**: Profile management with settings and account information
- **Contact/Support**: Contact forms and FAQ section
- **Dark/Light Mode**: Theme switching with system preference support

### Technical Features
- **Modern Stack**: Next.js 15, TypeScript, Tailwind CSS
- **UI Components**: shadcn/ui component library
- **Form Validation**: React Hook Form with Zod validation
- **Responsive Design**: Mobile-first responsive layout
- **Theme Support**: Dark/light mode with next-themes
- **Type Safety**: Full TypeScript implementation

## 🛠️ Technology Stack

- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **UI Components**: shadcn/ui
- **Form Handling**: React Hook Form + Zod
- **Theme**: next-themes
- **Icons**: Lucide React
- **Development**: Turbopack for fast development

## 📦 Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd taskflow-saas
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Run the development server**
   ```bash
   npm run dev
   ```

4. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

## 🏗️ Project Structure

```
taskflow-saas/
├── src/
│   ├── app/                    # Next.js App Router pages
│   │   ├── auth/              # Authentication pages
│   │   │   ├── signin/        # Sign in page
│   │   │   ├── signup/        # Sign up page
│   │   │   └── reset-password/ # Password reset
│   │   ├── dashboard/         # Dashboard pages
│   │   │   ├── profile/       # User profile
│   │   │   └── page.tsx       # Main dashboard
│   │   ├── contact/           # Contact page
│   │   ├── layout.tsx         # Root layout
│   │   └── page.tsx           # Landing page
│   ├── components/            # Reusable components
│   │   ├── ui/               # shadcn/ui components
│   │   ├── navigation.tsx    # Main navigation
│   │   ├── dashboard-nav.tsx # Dashboard navigation
│   │   ├── hero-section.tsx  # Landing page hero
│   │   ├── features-section.tsx # Features showcase
│   │   ├── pricing-section.tsx  # Pricing tiers
│   │   ├── footer.tsx        # Site footer
│   │   ├── theme-provider.tsx # Theme context
│   │   └── theme-toggle.tsx  # Theme switcher
│   └── lib/
│       └── utils.ts          # Utility functions
├── public/                   # Static assets
└── package.json             # Dependencies
```

## 🎨 Design System

The project uses a consistent design system built on:
- **Colors**: Neutral palette with primary accent
- **Typography**: Geist font family
- **Spacing**: Consistent spacing scale
- **Components**: Reusable shadcn/ui components
- **Responsive**: Mobile-first breakpoints

## 📱 Pages Overview

### Landing Page (`/`)
- Hero section with call-to-action
- Features showcase with icons and descriptions
- Pricing tiers with feature comparison
- Professional footer with links

### Authentication (`/auth/*`)
- **Sign In** (`/auth/signin`): Email/password login with social options
- **Sign Up** (`/auth/signup`): Account creation with validation
- **Reset Password** (`/auth/reset-password`): Password recovery flow

### Dashboard (`/dashboard`)
- Project overview with statistics
- Recent projects with progress tracking
- Upcoming tasks with priority indicators
- Team member avatars and collaboration info

### Profile (`/dashboard/profile`)
- Personal information management
- Profile picture upload
- Account status and billing info
- Settings and preferences

### Contact (`/contact`)
- Contact form with validation
- Contact information display
- FAQ section
- Category-based inquiry routing

## 🔧 Development

### Available Scripts

```bash
# Development server
npm run dev

# Production build
npm run build

# Start production server
npm start

# Linting
npm run lint
```

### Adding New Components

1. Use shadcn/ui CLI to add components:
   ```bash
   npx shadcn@latest add [component-name]
   ```

2. Components are automatically added to `src/components/ui/`

### Customizing Themes

Themes are configured in:
- `tailwind.config.js` - Color definitions
- `src/app/globals.css` - CSS custom properties
- `src/components/theme-provider.tsx` - Theme context

## 🚀 Deployment

### Vercel (Recommended)
1. Push code to GitHub
2. Connect repository to Vercel
3. Deploy automatically

### Other Platforms
The app can be deployed to any platform supporting Next.js:
- Netlify
- Railway
- DigitalOcean App Platform
- AWS Amplify

## 📝 Environment Variables

For production deployment, you may need:

```env
# Database (if adding backend)
DATABASE_URL=

# Authentication (if adding real auth)
NEXTAUTH_SECRET=
NEXTAUTH_URL=

# Email service (for contact forms)
SMTP_HOST=
SMTP_PORT=
SMTP_USER=
SMTP_PASS=
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- [Next.js](https://nextjs.org/) - React framework
- [shadcn/ui](https://ui.shadcn.com/) - UI component library
- [Tailwind CSS](https://tailwindcss.com/) - Utility-first CSS
- [Lucide](https://lucide.dev/) - Icon library
- [Vercel](https://vercel.com/) - Deployment platform

import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";
import { ThemeProvider } from "@/components/theme-provider";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "TaskFlow - Streamline Your Team's Workflow",
  description: "TaskFlow helps teams collaborate more effectively with powerful project management tools, real-time collaboration, and intelligent automation. Start your free trial today.",
  keywords: ["project management", "team collaboration", "task tracking", "workflow automation", "productivity"],
  authors: [{ name: "TaskFlow Team" }],
  creator: "TaskF<PERSON>",
  publisher: "TaskFlow",
  openGraph: {
    title: "TaskFlow - Streamline Your Team's Workflow",
    description: "TaskFlow helps teams collaborate more effectively with powerful project management tools, real-time collaboration, and intelligent automation.",
    url: "https://taskflow.com",
    siteName: "TaskFlow",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "TaskFlow - Streamline Your Team's Workflow",
    description: "TaskFlow helps teams collaborate more effectively with powerful project management tools, real-time collaboration, and intelligent automation.",
    creator: "@taskflow",
  },
  robots: {
    index: true,
    follow: true,
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange
        >
          {children}
        </ThemeProvider>
      </body>
    </html>
  );
}

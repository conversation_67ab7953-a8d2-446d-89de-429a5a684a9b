import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  Kanban, 
  Users, 
  Clock, 
  MessageSquare, 
  FileText, 
  BarChart3, 
  Shield, 
  Zap,
  Calendar,
  Target,
  Globe,
  Smartphone
} from "lucide-react";

const features = [
  {
    icon: Kanban,
    title: "Project Management",
    description: "Organize tasks with Kanban boards, Gantt charts, and custom workflows. Track progress and deadlines effortlessly.",
    badge: "Core"
  },
  {
    icon: Users,
    title: "Team Collaboration",
    description: "Real-time collaboration with team members. Share files, assign tasks, and communicate seamlessly.",
    badge: "Core"
  },
  {
    icon: Clock,
    title: "Time Tracking",
    description: "Built-in time tracking for accurate project billing and productivity insights. Generate detailed reports.",
    badge: "Pro"
  },
  {
    icon: MessageSquare,
    title: "Team Chat",
    description: "Integrated messaging system with channels, direct messages, and file sharing capabilities.",
    badge: "Core"
  },
  {
    icon: FileText,
    title: "Document Management",
    description: "Centralized document storage with version control, comments, and collaborative editing.",
    badge: "Pro"
  },
  {
    icon: BarChart3,
    title: "Analytics & Reporting",
    description: "Comprehensive analytics dashboard with custom reports and performance metrics.",
    badge: "Pro"
  },
  {
    icon: Shield,
    title: "Enterprise Security",
    description: "Bank-level security with SSO, 2FA, and compliance with SOC 2 and GDPR standards.",
    badge: "Enterprise"
  },
  {
    icon: Zap,
    title: "Automation",
    description: "Automate repetitive tasks with custom workflows, triggers, and integrations.",
    badge: "Pro"
  },
  {
    icon: Calendar,
    title: "Calendar Integration",
    description: "Sync with Google Calendar, Outlook, and other calendar apps for seamless scheduling.",
    badge: "Core"
  },
  {
    icon: Target,
    title: "Goal Tracking",
    description: "Set and track team goals with OKRs, milestones, and progress visualization.",
    badge: "Pro"
  },
  {
    icon: Globe,
    title: "Global Access",
    description: "Access your workspace from anywhere with cloud sync and offline capabilities.",
    badge: "Core"
  },
  {
    icon: Smartphone,
    title: "Mobile Apps",
    description: "Native iOS and Android apps with full feature parity and push notifications.",
    badge: "Core"
  }
];

export function FeaturesSection() {
  return (
    <section id="features" className="py-20 sm:py-32">
      <div className="container">
        <div className="mx-auto max-w-2xl text-center">
          <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">
            Everything you need to manage your team
          </h2>
          <p className="mt-6 text-lg leading-8 text-muted-foreground">
            TaskFlow provides all the tools your team needs to collaborate effectively, 
            track progress, and deliver projects on time.
          </p>
        </div>
        
        <div className="mx-auto mt-16 grid max-w-7xl grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
          {features.map((feature, index) => (
            <Card key={index} className="relative overflow-hidden">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center justify-center w-12 h-12 bg-primary/10 rounded-lg">
                    <feature.icon className="h-6 w-6 text-primary" />
                  </div>
                  <Badge 
                    variant={feature.badge === "Enterprise" ? "default" : feature.badge === "Pro" ? "secondary" : "outline"}
                    className="text-xs"
                  >
                    {feature.badge}
                  </Badge>
                </div>
                <CardTitle className="text-xl">{feature.title}</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-base">
                  {feature.description}
                </CardDescription>
              </CardContent>
            </Card>
          ))}
        </div>
        
        <div className="mt-16 text-center">
          <p className="text-sm text-muted-foreground">
            And many more features to help your team succeed
          </p>
        </div>
      </div>
    </section>
  );
}
